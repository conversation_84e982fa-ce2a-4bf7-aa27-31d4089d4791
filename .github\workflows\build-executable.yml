name: Build and Release Tomato Novel Downloader

on:
  push:
    branches: [ "main" ]
    tags: [ "v*" ]  # 打标签时发布

jobs:
  build-linux:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: <PERSON><PERSON> Dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyinstaller -r requirements.txt

      - name: Build Executable
        run: |
          if [ "${{ github.ref_type }}" = "tag" ]; then
            VERSION="${{ github.ref_name }}"
          else
            VERSION="sha-${{ github.sha }}"
          fi

          pyinstaller \
            --onefile \
            --strip \
            --exclude-module _bootlocale \
            --exclude-module _cffi_backend \
            --collect-data fake_useragent \
            --name=TomatoNovelDownloader-Linux_amd64-$VERSION \
            --clean \
            2.py

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: linux-build
          path: dist/TomatoNovelDownloader-Linux_amd64-*

  build-linux-arm64:
    runs-on: ubuntu-24.04-arm
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyinstaller -r requirements.txt

      - name: Build Executable
        run: |
          if [ "${{ github.ref_type }}" = "tag" ]; then
            VERSION="${{ github.ref_name }}"
          else
            VERSION="sha-${{ github.sha }}"
          fi

          pyinstaller \
            --onefile \
            --strip \
            --exclude-module _bootlocale \
            --exclude-module _cffi_backend \
            --collect-data fake_useragent \
            --name=TomatoNovelDownloader-Linux_arm64-$VERSION \
            --clean \
            2.py

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: linux-arm64-build
          path: dist/TomatoNovelDownloader-Linux_arm64-*

  build-windows:
    runs-on: windows-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Download UPX
        run: |
          $url = "https://github.com/upx/upx/releases/download/v5.0.0/upx-5.0.0-win64.zip"
          Invoke-WebRequest -Uri $url -OutFile upx.zip
          Expand-Archive -Path upx.zip -DestinationPath upx

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyinstaller -r requirements.txt

      - name: Build Executable
        run: |
          $upxDir = "$pwd\upx\upx-5.0.0-win64"
          if (Test-Path "$upxDir\upx.exe") {
              icacls "$upxDir\upx.exe" /grant Everyone:F
          }

          if ("${{ github.ref_type }}" -eq "tag") {
            $version = "${{ github.ref_name }}"
          } else {
            $version = "sha-${{ github.sha }}"
          }

          pyinstaller --onefile `
            --upx-dir "$upxDir" `
            --collect-data fake_useragent `
            --name=TomatoNovelDownloader-Win64-$version `
            --clean `
            2.py

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: windows-build
          path: dist\TomatoNovelDownloader-Win64-*.exe

  build-macos:
    runs-on: macos-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyinstaller -r requirements.txt

      - name: Build Executable
        run: |
          if [ "${{ github.ref_type }}" = "tag" ]; then
            VERSION="${{ github.ref_name }}"
          else
            VERSION="sha-${{ github.sha }}"
          fi

          pyinstaller \
            --onefile \
            --strip \
            --exclude-module _bootlocale \
            --exclude-module _cffi_backend \
            --collect-data fake_useragent \
            --name=TomatoNovelDownloader-macOS_arm64-$VERSION \
            --clean \
            2.py

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: macos-build
          path: dist/TomatoNovelDownloader-macOS_arm64-*

  publish-release:
    needs: [build-linux, build-linux-arm64, build-windows, build-macos]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')  # 仅打标签时发布

    steps:
      - name: Download All Artifacts
        uses: actions/download-artifact@v4
        with:
          path: release-artifacts

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          files: |
            release-artifacts/linux-build/*
            release-artifacts/linux-arm64-build/*
            release-artifacts/windows-build/*.exe
            release-artifacts/macos-build/*
          body: |
            **番茄小说下载器 ${{ github.ref_name }}**  
            a **支持平台**：  
            - Linux (x86_64/ARM64)  
            - Windows (UPX 压缩)  
            - macOS (ARM64)  

            b **包含文件**：  
            - `TomatoNovelDownloader-Linux_*`  
            - `TomatoNovelDownloader-Win64-*.exe`  
            - `TomatoNovelDownloader-macOS_*`  

            c **源码地址**: https://github.com/${{ github.repository }}
